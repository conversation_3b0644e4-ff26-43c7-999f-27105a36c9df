import { runningClientSide } from '@benzinga/utils';
import { raptiveAdWrapperClassnames } from './constants';
import {
  captureEmailFromPageUrl,
  appendAdLightClassToBody,
  appendPaywallClassToBody,
  getReferrerSiteName,
  getUtmSourceName,
  injectRaptiveFooterAdScript,
  injectRaptiveScript,
  removeRaptiveFooterAdScript,
  isPaywallClassAppended,
  appendStagingClassToBody,
  appendSandboxClassToBody,
  detectAndRemoveEmailsFromUrl,
} from './utils';
import { RaptiveAdPlacementType, Targeting } from './entities';

interface AdItemInfo {
  width: number;
  height: number;
  networkCodes: string[];
  adUnitName: string;
  uniqueId: string;
  iframeId: string;
  index: number;
}

const PAGES_WHERE_ADS_ARE_DISABLED = [
  '/privacy-policy',
  '/terms-and-conditions',
  '/disclaimer',
  '/welcome',
  '/login',
  '/logout',
  '/sponsored',
];

class RaptiveAdManager {
  private INITIALIZED = false; // This means init() was called, otherwise ad script was injected directly by requestAdLoad()
  private IS_SCRIPT_INJECTING = false;
  private IS_SCRIPT_INJECTED = false;
  private DISABLED = false;
  private READY = false;

  private targeting: Targeting = {};

  private isFooterAdScriptInjected = false;
  private wasCaptureEmailFromPageUrlMethodCalled = false;

  private debounceTimer: NodeJS.Timeout | null = null;
  private readonly DEBOUNCE_DELAY = 1000;

  private registeredPlaceholders: Set<string> = new Set();
  private expectedPlaceholderCount = 0;
  private adCounters: Record<string, number> = {};

  private observer: MutationObserver | null = null;
  private observerTimeout: NodeJS.Timeout | null = null;

  private placeholderCheckTimeout: NodeJS.Timeout | null = null;
  private readonly PLACEHOLDER_CHECK_DELAY = 0;

  constructor() {
    this.log('Initializing RaptiveAdManager', 'info');
    if (runningClientSide()) {
      if (this.areAdsDisabledOnCurrentPage()) {
        this.disable();
        this.log('Ads disabled on current page', 'warn');
        return;
      } else {
        // Set up MutationObserver to watch for React placeholders
        this.log('Setting up MutationObserver to watch for React-rendered ad placeholders', 'info');
        this.setupReactPlaceholderWatcher();
      }
    }
  }

  public isLoaded(): boolean {
    return this.IS_SCRIPT_INJECTED;
  }

  public isDisabled(): boolean {
    return this.DISABLED;
  }

  public isReady(): boolean {
    return this.READY;
  }

  public getTargeting(): Targeting {
    return this.targeting;
  }

  public setTargeting(newTargeting: Targeting): Targeting {
    this.targeting = newTargeting;
    return this.targeting;
  }

  public setReady(isReady: boolean): boolean {
    this.READY = isReady;
    if (isReady) {
      this.enable();
      this.initAdLoading();
    }
    return this.READY;
  }

  public init(targeting: Record<string, string | string[] | boolean> = {}): void {
    try {
      if (!runningClientSide()) return;
      if (this.DISABLED || this.INITIALIZED) return;

      const referrer = this.getReferrer();
      if (referrer) targeting.referrer = referrer;
      targeting.paywalled = this.getIsPaywalled();
      targeting && this.setTargeting(targeting);

      this.INITIALIZED = true;
    } catch (error) {
      this.log('Error initializing: ', 'error', error);
    }
  }

  public refresh(): void {
    this.IS_SCRIPT_INJECTED = false;
    this.loadAdScript();
    this.log('Ad manager refreshed', 'info');
  }

  public forceLoadAdScript(): void {
    if (this.IS_SCRIPT_INJECTED) {
      this.log('Ad script already injected, skipping force load', 'debug');
      return;
    }
    this.log('Force loading ad script', 'info');
    this.loadAdScript(true);
  }

  public cleanup(): void {
    this.clearDebounceTimer();
    this.disconnectObserver();
    this.clearPlaceholderCheckTimeout();
  }

  public disable(): void {
    this.DISABLED = true;
    this.clearDebounceTimer();
    this.disableAllRaptiveAds();
  }

  public enable(): void {
    if (this.areAdsDisabledOnCurrentPage()) return;
    this.DISABLED = false;
  }

  public enableAdLightMode(): void {
    appendAdLightClassToBody();
  }

  public enablePaywallMode(): void {
    appendPaywallClassToBody();
  }

  public enableEnvironmentBodyClass(): void {
    appendStagingClassToBody();
    appendSandboxClassToBody();
  }

  public generateAdId(type: RaptiveAdPlacementType): string {
    if (!this.adCounters[type]) {
      this.adCounters[type] = 0;
    }
    this.adCounters[type]++;
    return `raptive-ad-${type}-${this.adCounters[type]}`;
  }

  public requestAdLoad(type: RaptiveAdPlacementType): string | null {
    if (this.DISABLED || !this.INITIALIZED) return null;
    const id = this.generateAdId(type);
    return id;
  }

  public getRegisteredPlaceholders(): string[] {
    return Array.from(this.registeredPlaceholders);
  }

  public getRegisteredPlaceholderCount(): number {
    return this.registeredPlaceholders.size;
  }

  public logAdElementsWithPlaceholderLengths(): void {
    if (!runningClientSide()) return;

    const adPlacements = this.getAdPlacementsInDOM();
    const adPlaceholderNames = this.getAdPlaceholderInDOM();

    this.log('Ad elements with placeholder lengths:', 'debug');

    adPlacements.forEach((placement, index) => {
      const placeholderName = adPlaceholderNames[index] || 'unknown';
      const placeholderLength = placement.children.length;
      this.log(`  ${placeholderName}: ${placeholderLength} placeholder(s)`, 'debug');
    });
  }

  public logPlacementsBeforeScriptInjection(): void {
    if (!runningClientSide()) return;

    const adPlacements = this.getAdPlacementsInDOM();
    const registeredPlaceholders = this.getRegisteredPlaceholders();

    this.log(
      `Placements before script injection: ${adPlacements.length} in DOM, ${this.expectedPlaceholderCount} expected, ${registeredPlaceholders.length} registered`,
      'debug',
    );

    if (adPlacements.length > 0) {
      Array.from(adPlacements).forEach((placement, index) => {
        const type = this.getAdPlaceholderInDOM()[index] || 'unknown';
        const hasContent = placement.children.length > 0;
        const visible = placement.getBoundingClientRect().width > 0;
        const id = placement.id || `placement-${index + 1}`;

        this.log(`Placement ${index + 1}: ${id} (${type}) - content: ${hasContent}, visible: ${visible}`, 'debug');
      });
    }

    if (adPlacements.length === 0) {
      this.log('No ad placements found in DOM before script injection', 'warn');
    } else if (this.expectedPlaceholderCount > 0 && adPlacements.length < this.expectedPlaceholderCount) {
      this.log(
        `Found fewer placements (${adPlacements.length}) than expected (${this.expectedPlaceholderCount})`,
        'warn',
      );
    }
  }

  public registerPlaceholder(type: RaptiveAdPlacementType): string | null {
    if (this.DISABLED) return null;
    const id = this.generateAdId(type);
    this.log(`Registered placeholder: ${id} (${type})`, 'debug');
    this.registeredPlaceholders.add(id);
    return id;
  }

  public unregisterPlaceholder(id: string): void {
    this.registeredPlaceholders.delete(id);
  }

  public setExpectedPlaceholderCount(count: number): void {
    this.expectedPlaceholderCount = count;
    this.log(`Expected placeholder count: ${count}`, 'debug');
  }

  public setExpectedPlaceholderCountAndReady(count: number, isReady: boolean): void {
    if (this.expectedPlaceholderCount === count && this.READY === isReady) {
      this.log(`Skipping redundant update - count: ${count}, ready: ${isReady}`, 'debug');
      return;
    }

    this.expectedPlaceholderCount = count;
    this.READY = isReady;
    this.log(`Updated state - count: ${count}, ready: ${isReady}`, 'info');

    if (isReady) {
      this.initAdLoading();
    }
  }

  public checkForRaptiveAdPlaceholders(): boolean {
    if (!runningClientSide()) return false;
    const adPlacements = this.getAdPlacementsInDOM();
    return adPlacements.length > 0;
  }

  public injectFooterAdScript({ disabled }: { disabled?: boolean } = {}): void {
    if (disabled || this.DISABLED || this.isFooterAdScriptInjected) return;
    this.isFooterAdScriptInjected = true;
    injectRaptiveFooterAdScript();
  }

  public removeFooterAdScript(): void {
    this.isFooterAdScriptInjected = false;
    removeRaptiveFooterAdScript();
  }

  public captureEmailFromPageUrl({ disabled = false }): void {
    if (disabled || this.DISABLED || this.wasCaptureEmailFromPageUrlMethodCalled) return;
    this.wasCaptureEmailFromPageUrlMethodCalled = true;
    this.log('Capturing email from page URL', 'debug');
    captureEmailFromPageUrl();
  }

  public areAdsDisabledOnCurrentPage(): boolean {
    if (!runningClientSide()) return false;
    // Required check for mobile app compatibility
    if (typeof window?.location?.pathname === 'undefined') return false;
    return PAGES_WHERE_ADS_ARE_DISABLED.includes(window.location.pathname);
  }

  private initAdLoading(): void {
    this.log(`Initializing ad loading - expected placeholders: ${this.expectedPlaceholderCount}`, 'info');

    // Check if placeholders are already in DOM
    console.log('checkIfAdPlaceholdersInDOMMeetsExpected', this.checkIfAdPlaceholdersInDOMMeetsExpected());
    if (this.checkIfAdPlaceholdersInDOMMeetsExpected()) {
      this.log('Expected placeholders found in DOM, loading ad script', 'info');
      this.logAdElementsWithPlaceholderLengths();
      this.loadAdScript();
      return;
    }

    // Set up observer to watch for placeholders
    this.log('Setting up MutationObserver to watch for ad placeholders', 'info');
    this.setupAdPlaceholderObserver();
  }

  private getAdPlacementsInDOM(): Element[] {
    // Look for children of .raptive-ad-placement that have the wrapper classnames
    const raptiveAdWrapperSelectors = Object.values(raptiveAdWrapperClassnames)
      .map(className => `.raptive-ad-placement .${className}`)
      .join(', ');

    const adContainers = document.querySelectorAll(raptiveAdWrapperSelectors);
    return Array.from(adContainers);
  }

  private getAdPlaceholderInDOM(): string[] {
    const adPlaceholders = this.getAdPlacementsInDOM();
    return adPlaceholders.map(adPlaceholder => {
      // Find which wrapper classname this element has
      const wrapperClassnames = Object.values(raptiveAdWrapperClassnames);
      for (const className of wrapperClassnames) {
        if (adPlaceholder.classList.contains(className)) {
          return className;
        }
      }
      return 'unknown';
    });
  }

  private getLoadedAdsInDOM(): Record<string, AdItemInfo> {
    if (!runningClientSide()) return {};
    const adSizes: Record<string, AdItemInfo> = {};
    const raptiveAdWrapperSelectors = Object.values(raptiveAdWrapperClassnames)
      .map(className => `.${className}`)
      .join(', ');
    const adContainers = document.querySelectorAll(raptiveAdWrapperSelectors);
    adContainers.forEach(adContainer => {
      const iframe: HTMLIFrameElement | null =
        adContainer.querySelector("iframe[data-load-complete='true']") ||
        adContainer.querySelector("iframe[aria-label='Advertisement']");
      if (iframe?.id) {
        const width = parseInt(iframe.width, 10) || iframe.offsetWidth;
        const height = parseInt(iframe.height, 10) || iframe.offsetHeight;
        if (width && height) {
          const idParts = iframe.id.replace('google_ads_iframe', '').split('/');
          if (idParts) {
            const adUnitName = idParts?.[2];
            adSizes[adUnitName] = {
              adUnitName,
              height,
              iframeId: iframe.id,
              index: parseInt(idParts?.[2], 10) || 0,
              networkCodes: idParts?.[1]?.split(','),
              uniqueId: idParts?.[3] || '',
              width,
            };
          }
        }
      }
    });
    return adSizes;
  }

  private getAdHeight(adId: string): { width: number; height: number } | null {
    if (!runningClientSide()) return null;

    const adContainer = document.getElementById(adId) as HTMLDivElement | null;
    if (!adContainer) return null;

    const iframe = adContainer.querySelector('iframe') as HTMLIFrameElement | null;
    if (!iframe) return null;

    return {
      height: parseInt(iframe.height),
      width: parseInt(iframe.width),
    };
  }

  private setupReactPlaceholderWatcher(): void {
    if (!runningClientSide() || this.observer) return;

    this.observer = new MutationObserver(mutations => {
      let hasRelevantChanges = false;

      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          for (const node of Array.from(mutation.addedNodes)) {
            if (node instanceof HTMLElement) {
              if (node.classList.contains('raptive-ad-placement') || node.querySelector('.raptive-ad-placement')) {
                this.log('React ad placement detected in DOM', 'info');
                hasRelevantChanges = true;
                break;
              }
            }
          }
          if (hasRelevantChanges) break;
        }
      }

      if (hasRelevantChanges) {
        if (this.IS_SCRIPT_INJECTED) {
          this.log('Script already injected, ignoring new placeholders', 'debug');
          return;
        }

        const placeholders = this.getAdPlacementsInDOM();
        if (placeholders.length > 0) {
          this.log(`Found ${placeholders.length} ad placeholders, initializing ad loading`, 'info');
          this.disconnectObserver();
          this.initAdLoading();
        }
      }
    });

    const targetNode = document.querySelector('.article-content-body') || document.body;
    this.observer.observe(targetNode, {
      childList: true,
      subtree: true,
    });

    this.log('MutationObserver started watching for React ad placeholders', 'info');
  }

  private setupAdPlaceholderObserver(noDebounce?: false): void {
    if (!runningClientSide() || !this.READY || this.observer) return;

    const callback = () => {
      if (this.IS_SCRIPT_INJECTED) {
        this.log('Script already injected, disconnecting observer', 'debug');
        this.disconnectObserver();
        return;
      }

      if (this.expectedPlaceholderCount > 0 && this.checkIfAdPlaceholdersInDOMMeetsExpected()) {
        this.log(`Expected placeholders met, loading ad script (${this.getAdPlaceholderInDOM().length} found)`, 'info');
        this.logAdElementsWithPlaceholderLengths();
        this.loadAdScript();
        this.disconnectObserver();
      } else if (this.expectedPlaceholderCount === 0 && this.getAdPlaceholderInDOM().length > 0) {
        this.log(`No expected placeholders, loading ad script (${this.getAdPlaceholderInDOM().length} found)`, 'info');
        this.logAdElementsWithPlaceholderLengths();
        this.loadAdScript();
        this.disconnectObserver();
      }
    };

    if (noDebounce) {
      callback();
      return;
    }

    let debounceTimeout: NodeJS.Timeout | null = null;
    const debouncedObserverCallback = () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }

      debounceTimeout = setTimeout(() => {
        callback();
      }, 50);
    };

    this.observer = new MutationObserver(mutations => {
      let hasRelevantChanges = false;

      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          for (const node of Array.from(mutation.addedNodes)) {
            if (node instanceof HTMLElement) {
              if (node.classList.contains('raptive-ad-placement') || node.querySelector('.raptive-ad-placement')) {
                this.log('Found raptive ad placement in DOM mutation', 'debug');
                hasRelevantChanges = true;
                break;
              }
            }
          }

          if (hasRelevantChanges) {
            break;
          }
        }
      }

      if (hasRelevantChanges) {
        debouncedObserverCallback();
      }
    });

    const targetNode = document.querySelector('.article-content-body') || document.body;
    this.observer.observe(targetNode, {
      childList: true,
      subtree: true,
    });

    // No timeout - let MutationObserver handle detection naturally
  }

  private getReasonForSkippingAdScriptInjection(): string[] {
    const reasons: string[] = [];

    if (this.DISABLED) {
      reasons.push('Raptive Ad Manager is disabled');
    }

    if (this.IS_SCRIPT_INJECTED) {
      reasons.push('Raptive ad script has already been loaded');
    }

    if (!this.READY) {
      reasons.push('Raptive Ad Manager is not ready');
    }

    if (this.expectedPlaceholderCount && !this.checkIfAdPlaceholdersInDOMMeetsExpected()) {
      reasons.push(
        `Expected (${this.expectedPlaceholderCount}) placeholders, but found (${this.getAdPlacementsInDOM().length}) placeholders`,
        `Registered placeholders (${this.getRegisteredPlaceholders().length}): ${this.getRegisteredPlaceholders().join(', ') || 'NONE'}`,
        `Placeholders in DOM (${this.getAdPlacementsInDOM().length}): ${this.getAdPlaceholderInDOM().join(', ') || 'NONE'}`,
      );
    }

    return reasons;
  }

  private isReadyToInjectAdScript(): boolean {
    const areExpectedAdsMissing = this.expectedPlaceholderCount && !this.checkIfAdPlaceholdersInDOMMeetsExpected();
    if (this.DISABLED || this.IS_SCRIPT_INJECTED || !this.READY || areExpectedAdsMissing) {
      const reasons = this.getReasonForSkippingAdScriptInjection();

      console.log('this.READY', this.READY);

      if (reasons.length > 0) {
        const reasonsList = reasons.join(', ');
        this.log(`Skipping ad script injection because: ${reasonsList}`, 'warn');
      } else {
        this.log('Skipping ad script injection', 'warn');
      }

      return false;
    }

    return true;
  }

  private executeLoadAdScript = (force?: boolean): void => {
    if (!runningClientSide()) return;

    //if (!force) return;

    const targeting = this.getTargeting();

    if (!force && !this.isReadyToInjectAdScript()) return;
    if (this.areAdsDisabledOnCurrentPage()) {
      this.log('Ads disabled on current page - skipping script injection', 'warn');
      return;
    }

    if (!this.IS_SCRIPT_INJECTING) {
      this.IS_SCRIPT_INJECTING = true;
      this.detectAndRemoveEmailsFromUrl();
      this.log('Injecting ad script', 'info');
      this.logPlacementsBeforeScriptInjection();
      injectRaptiveScript({
        onError: () => {
          this.IS_SCRIPT_INJECTING = false;
          this.clearDebounceTimer();
          this.log('Ad script injection failed', 'error');
        },
        onLoad: () => {
          this.log('Ad script injected successfully', 'info');
          this.IS_SCRIPT_INJECTED = true;
          this.IS_SCRIPT_INJECTING = false;
          this.clearDebounceTimer();
          this.clearPlaceholderCheckTimeout();

          this.injectFooterAdScript();
          this.captureEmailFromPageUrl({});
        },
        pageTargeting: targeting,
      });
    } else {
      this.log('Ad script injection already in progress', 'debug');
    }
  };

  private detectAndRemoveEmailsFromUrl = async () => {
    if (!runningClientSide()) return;
    if (!window.location.search) return;
    this.log('Detecting and removing emails from URL', 'debug');
    await detectAndRemoveEmailsFromUrl();
  };

  private clearPlaceholderCheckTimeout(): void {
    if (this.placeholderCheckTimeout) {
      clearTimeout(this.placeholderCheckTimeout);
      this.placeholderCheckTimeout = null;
    }
  }

  private checkIfAdPlaceholdersInDOMMeetsExpected(): boolean {
    if (!runningClientSide()) return false;
    const adContainers = this.getAdPlacementsInDOM();
    this.log(
      `Checking placeholders in DOM: found ${adContainers.length}, expected ${this.expectedPlaceholderCount}`,
      'info',
    );

    // Log details of found placeholders
    if (adContainers.length > 0) {
      this.log('Found placeholders:', 'info');
      adContainers.forEach((_, index) => {
        const className = this.getAdPlaceholderInDOM()[index] || 'unknown';
        this.log(`  ${index + 1}: (${className})`, 'info');
      });
    } else {
      this.log('No placeholders found in DOM', 'info');
    }

    if (this.expectedPlaceholderCount === 0) {
      // Allow script injection if any placeholders are found
      return adContainers.length > 0;
    }

    // Be more lenient - if we have at least 70% of expected placeholders or at least 2 placeholders,
    // consider it good enough to start loading ads for faster rendering
    const minThreshold = Math.max(2, Math.floor(this.expectedPlaceholderCount * 0.7));
    const result = adContainers.length >= minThreshold;

    if (result && adContainers.length < this.expectedPlaceholderCount) {
      this.log(
        `Loading ads with partial placeholders (${adContainers.length}/${this.expectedPlaceholderCount}) for faster rendering`,
        'info',
      );
    }

    return result;
  }

  private getReferrer(): string | null {
    const referrer = getUtmSourceName() || getReferrerSiteName();
    return referrer;
  }

  private getIsPaywalled(): boolean {
    if (!runningClientSide()) return false;
    return isPaywallClassAppended();
  }

  private disconnectObserver(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    if (this.observerTimeout) {
      clearTimeout(this.observerTimeout);
      this.observerTimeout = null;
    }
  }

  private clearDebounceTimer(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  private loadAdScript = (force?: boolean): void => {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // this.debounceTimer = setTimeout(() => {
    //   this.executeLoadAdScript(force);
    // }, this.DEBOUNCE_DELAY);
    this.executeLoadAdScript(force);
  };

  private disableAllRaptiveAds(): void {
    if (Array.isArray(window?.adthrive?.cmd)) {
      window.adthrive.cmd.push(function () {
        if (typeof window.adthrive.disableAds === 'function') {
          window.adthrive.disableAds();
        }
      });
    }
  }

  private log(
    message: string,
    method: 'log' | 'info' | 'debug' | 'table' | 'warn' | 'error' = 'log',
    ...optionalParams: unknown[]
  ): void {
    if (!runningClientSide()) return;
    // Temporarily allow all logs for debugging
    // if (
    //   typeof window.location !== 'undefined' &&
    //   new URLSearchParams(window.location.search).get('debug_raptive_ad_manager') !== 'true'
    // )
    //   return;

    const prefix = '[Raptive Ad Manager] ';

    switch (method) {
      case 'table':
        console.group(`${prefix}${message}`);
        console.table(...optionalParams);
        console.groupEnd();
        break;
      case 'info':
        console.info(`${prefix}${message}`, ...optionalParams);
        break;
      case 'debug':
        console.debug(`${prefix}${message}`, ...optionalParams);
        break;
      case 'warn':
        console.warn(`${prefix}${message}`, ...optionalParams);
        break;
      case 'error':
        console.error(`${prefix}${message}`, ...optionalParams);
        break;
      case 'log':
      default:
        console.log(`${prefix}${message}`, ...optionalParams);
        break;
    }
  }
}

export const raptiveAdManager = new RaptiveAdManager();
